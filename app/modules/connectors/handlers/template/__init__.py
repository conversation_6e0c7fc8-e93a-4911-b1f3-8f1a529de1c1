"""
Template Connector

This module provides a template for creating new connectors following the established pattern.
It includes all necessary components for:
- Schema-driven development
- Graph database operations  
- Pinecone embedding storage
- Semantic search capabilities
- Entity and relationship mapping

Main Components:
- TemplateConnectorService: Main connector implementation
- TemplateConnection: Connection management
- Schema definitions and constants
- Entity and relationship types

Usage:
1. Copy this template to your new connector location
2. Replace "template" with your connector name throughout all files
3. Update the schema definition with your specific entities and relationships
4. Implement the connection logic for your data source
5. Implement the entity mapping logic
6. Update the queries for your specific use case
"""

from .service import TemplateConnectorService
from .connection import TemplateConnection
from .schema import TemplateConnectorConfig

__all__ = [
    'TemplateConnectorService',
    'TemplateConnection', 
    'TemplateConnectorConfig'
]
