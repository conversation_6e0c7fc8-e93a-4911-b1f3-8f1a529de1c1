"""
Template Advanced Search Service

Implements comprehensive search capabilities for Template data with multiple strategies.
Supports searching across documents, users, projects, and other entities.

CUSTOMIZATION INSTRUCTIONS:
1. Replace "Template" with your connector name throughout this file
2. Update the entity type mappings to match your schema
3. Update the relationship mappings for graph traversal
4. Implement your specific search strategies
5. Update the search result processing logic
"""

import logging
import asyncio
import hashlib
import json
from typing import List, Dict, Any, Optional, Set, Tuple
from datetime import datetime, timedelta
from enum import Enum

from app.services.neo4j_service import execute_read_query, execute_write_query
from app.utils.redis.redis_service import RedisService
from app.utils.pinecone.pinecone_service import PineconeService

logger = logging.getLogger(__name__)


class TemplateSearchType(Enum):
    """Template entity search types - CUSTOMIZE for your entities"""
    DOCUMENT = "document"
    USER = "user"
    PROJECT = "project"
    ALL = "all"


class SearchStrategy(Enum):
    """Search strategy types"""
    GRAPH_TRAVERSAL = "graph_traversal"
    SEMANTIC_ONLY = "semantic_only"
    ENTITY_CENTRIC = "entity_centric"
    RELATIONSHIP_CENTRIC = "relationship_centric"
    HYBRID = "hybrid"


class TemplateSearchService:
    """Advanced Template search service with multiple strategies"""
    
    def __init__(self, pinecone_service: PineconeService = None, redis_service: RedisService = None):
        self.pinecone_service = pinecone_service
        self.redis_service = redis_service
        self.cache_ttl = 300  # 5 minutes
        
        # Template entity type mappings - CUSTOMIZE for your entities
        self.entity_types = {
            'document': 'TemplateDocument',
            'user': 'TemplateUser',
            'project': 'TemplateProject',
        }
        
        # Relationship mappings for graph traversal - CUSTOMIZE for your relationships
        self.relationship_types = {
            'creates': 'CREATES',
            'member_of': 'MEMBER_OF',
            'belongs_to': 'BELONGS_TO',
            'references': 'REFERENCES',
            'collaborates_on': 'COLLABORATES_ON',
            'similar_to': 'SIMILAR_TO',
            'related_to': 'RELATED_TO'
        }

    async def search(self, query: str, organisation_id: str, user_id: str = None,
                    search_type: TemplateSearchType = TemplateSearchType.ALL, 
                    strategy: SearchStrategy = SearchStrategy.HYBRID, 
                    limit: int = 20, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Main search entry point with intelligent strategy selection
        
        CUSTOMIZE: Update the search logic for your specific needs
        
        Args:
            query: Search query string
            organisation_id: Organisation ID
            user_id: Optional user ID for user-specific searches
            search_type: Type of Template entity to search
            strategy: Search strategy to use
            limit: Maximum results to return
            filters: Additional search filters
            
        Returns:
            Dict containing search results and metadata
        """
        try:
            logger.debug(f"Template search: query='{query}', type={search_type.value}, strategy={strategy.value}")
            
            # Check cache first
            cache_key = self._generate_cache_key(query, organisation_id, search_type, strategy, filters)
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                return cached_result

            # Determine optimal strategy if not specified
            if strategy == SearchStrategy.HYBRID:
                optimal_strategy = await self._determine_optimal_strategy(query, search_type, filters)
                if optimal_strategy != SearchStrategy.HYBRID:
                    strategy = optimal_strategy

            # Execute search based on strategy
            if strategy == SearchStrategy.SEMANTIC_ONLY:
                results = await self._semantic_search(query, organisation_id, search_type, limit, filters)
            elif strategy == SearchStrategy.GRAPH_TRAVERSAL:
                results = await self._graph_traversal_search(query, organisation_id, search_type, limit, filters)
            elif strategy == SearchStrategy.ENTITY_CENTRIC:
                results = await self._entity_centric_search(query, organisation_id, search_type, limit, filters)
            elif strategy == SearchStrategy.RELATIONSHIP_CENTRIC:
                results = await self._relationship_centric_search(query, organisation_id, search_type, limit, filters)
            else:  # HYBRID
                results = await self._hybrid_search(query, organisation_id, search_type, limit, filters)

            # If no results found and not already using fallback, try fallback search
            if results.get('total_count', 0) == 0 and not results.get('strategy_used', '').startswith('fallback'):
                logger.info(f"No results found with {strategy.value}, trying fallback search")
                results = await self._fallback_search(query, organisation_id, search_type, limit, filters)
            
            # Cache results
            self._cache_result(cache_key, results)
            
            return results
            
        except Exception as e:
            logger.error(f"Template search failed: {str(e)}")
            return {
                'results': [],
                'total_count': 0,
                'search_time_ms': 0,
                'strategy_used': strategy.value,
                'error': str(e)
            }

    async def _semantic_search(self, query: str, organisation_id: str, 
                              search_type: TemplateSearchType, limit: int, 
                              filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Perform semantic search using Pinecone embeddings
        
        CUSTOMIZE: Update for your embedding metadata structure
        """
        try:
            start_time = datetime.now()
            
            if not self.pinecone_service:
                raise Exception("Pinecone service not available")
            
            # Prepare search filters
            search_filters = {'organisation_id': organisation_id}
            
            if search_type != TemplateSearchType.ALL:
                search_filters['entity_type'] = self.entity_types.get(search_type.value)
            
            if filters:
                search_filters.update(filters)
            
            # Perform semantic search
            success, message, search_results = self.pinecone_service.search_vectors(
                query_text=query,
                organisation_id=organisation_id,
                top_k=limit,
                filters=search_filters
            )
            
            if not success:
                raise Exception(f"Pinecone search failed: {message}")
            
            # Process results
            results = []
            for match in search_results.matches:
                metadata = match.metadata
                results.append({
                    'id': metadata.get('id'),
                    'title': metadata.get('title', ''),
                    'content': metadata.get('content', ''),
                    'entity_type': metadata.get('entity_type'),
                    'score': float(match.score),
                    'metadata': metadata,
                    'search_method': 'semantic'
                })
            
            search_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'results': results,
                'total_count': len(results),
                'search_time_ms': search_time_ms,
                'strategy_used': 'semantic_only'
            }
            
        except Exception as e:
            logger.error(f"Semantic search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, search_type, limit, filters)

    async def _graph_traversal_search(self, query: str, organisation_id: str,
                                     search_type: TemplateSearchType, limit: int,
                                     filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Perform graph traversal search using Neo4j
        
        CUSTOMIZE: Update the graph queries for your specific relationships
        """
        try:
            start_time = datetime.now()
            
            # Build graph traversal query based on search type
            if search_type == TemplateSearchType.DOCUMENT:
                cypher_query = self._build_document_graph_query(query, filters)
            elif search_type == TemplateSearchType.USER:
                cypher_query = self._build_user_graph_query(query, filters)
            elif search_type == TemplateSearchType.PROJECT:
                cypher_query = self._build_project_graph_query(query, filters)
            else:
                cypher_query = self._build_general_graph_query(query, filters)
            
            # Execute query
            params = {
                'organisation_id': organisation_id,
                'query': query,
                'limit': limit
            }
            if filters:
                params.update(filters)
            
            neo4j_results = execute_read_query(cypher_query, params)
            
            # Process results
            results = []
            for record in neo4j_results:
                entity = record.get('entity')
                if entity:
                    results.append({
                        'id': entity.get('id'),
                        'title': entity.get('title') or entity.get('name') or entity.get('display_name', ''),
                        'content': entity.get('content', ''),
                        'entity_type': record.get('entity_type', ''),
                        'score': record.get('score', 1.0),
                        'metadata': dict(entity),
                        'search_method': 'graph_traversal',
                        'path_info': record.get('path_info', {})
                    })
            
            search_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'results': results,
                'total_count': len(results),
                'search_time_ms': search_time_ms,
                'strategy_used': 'graph_traversal'
            }
            
        except Exception as e:
            logger.error(f"Graph traversal search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, search_type, limit, filters)

    async def _hybrid_search(self, query: str, organisation_id: str,
                            search_type: TemplateSearchType, limit: int,
                            filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Perform hybrid search combining semantic and graph traversal
        
        CUSTOMIZE: Adjust the weighting and combination logic for your needs
        """
        try:
            start_time = datetime.now()
            
            # Run both searches concurrently
            semantic_task = self._semantic_search(query, organisation_id, search_type, limit//2, filters)
            graph_task = self._graph_traversal_search(query, organisation_id, search_type, limit//2, filters)
            
            semantic_results, graph_results = await asyncio.gather(semantic_task, graph_task)
            
            # Combine and deduplicate results
            combined_results = []
            seen_ids = set()
            
            # Add semantic results with higher weight for semantic score
            for result in semantic_results.get('results', []):
                if result['id'] not in seen_ids:
                    result['combined_score'] = result['score'] * 0.7  # Weight semantic results
                    combined_results.append(result)
                    seen_ids.add(result['id'])
            
            # Add graph results with weight for graph relevance
            for result in graph_results.get('results', []):
                if result['id'] not in seen_ids:
                    result['combined_score'] = result['score'] * 0.5  # Weight graph results
                    combined_results.append(result)
                    seen_ids.add(result['id'])
                else:
                    # Boost score for items found in both searches
                    for existing in combined_results:
                        if existing['id'] == result['id']:
                            existing['combined_score'] += result['score'] * 0.3
                            existing['search_method'] = 'hybrid'
                            break
            
            # Sort by combined score
            combined_results.sort(key=lambda x: x.get('combined_score', 0), reverse=True)
            
            # Limit results
            final_results = combined_results[:limit]
            
            search_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'results': final_results,
                'total_count': len(final_results),
                'search_time_ms': search_time_ms,
                'strategy_used': 'hybrid',
                'semantic_count': len(semantic_results.get('results', [])),
                'graph_count': len(graph_results.get('results', []))
            }
            
        except Exception as e:
            logger.error(f"Hybrid search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, search_type, limit, filters)

    # Helper methods - CUSTOMIZE these for your specific graph queries

    def _build_document_graph_query(self, query: str, filters: Dict[str, Any] = None) -> str:
        """Build graph query for document search - CUSTOMIZE for your schema"""
        return """
        MATCH (d:TemplateDocument {organisation_id: $organisation_id})
        WHERE d.title CONTAINS $query OR d.content CONTAINS $query
        OPTIONAL MATCH (d)<-[:CREATES]-(u:TemplateUser)
        OPTIONAL MATCH (d)-[:BELONGS_TO]->(p:TemplateProject)
        RETURN d as entity, 'TemplateDocument' as entity_type,
               score() as score,
               {author: u.display_name, project: p.name} as path_info
        ORDER BY score DESC
        LIMIT $limit
        """

    def _build_user_graph_query(self, query: str, filters: Dict[str, Any] = None) -> str:
        """Build graph query for user search - CUSTOMIZE for your schema"""
        return """
        MATCH (u:TemplateUser {organisation_id: $organisation_id})
        WHERE u.display_name CONTAINS $query OR u.username CONTAINS $query
        OPTIONAL MATCH (u)-[:CREATES]->(d:TemplateDocument)
        OPTIONAL MATCH (u)-[:MEMBER_OF]->(p:TemplateProject)
        RETURN u as entity, 'TemplateUser' as entity_type,
               score() as score,
               {documents_count: count(DISTINCT d), projects_count: count(DISTINCT p)} as path_info
        ORDER BY score DESC
        LIMIT $limit
        """

    def _build_project_graph_query(self, query: str, filters: Dict[str, Any] = None) -> str:
        """Build graph query for project search - CUSTOMIZE for your schema"""
        return """
        MATCH (p:TemplateProject {organisation_id: $organisation_id})
        WHERE p.name CONTAINS $query OR p.description CONTAINS $query
        OPTIONAL MATCH (p)<-[:BELONGS_TO]-(d:TemplateDocument)
        OPTIONAL MATCH (p)<-[:MEMBER_OF]-(u:TemplateUser)
        RETURN p as entity, 'TemplateProject' as entity_type,
               score() as score,
               {documents_count: count(DISTINCT d), members_count: count(DISTINCT u)} as path_info
        ORDER BY score DESC
        LIMIT $limit
        """

    def _build_general_graph_query(self, query: str, filters: Dict[str, Any] = None) -> str:
        """Build general graph query for all entities - CUSTOMIZE for your schema"""
        return """
        CALL {
            MATCH (d:TemplateDocument {organisation_id: $organisation_id})
            WHERE d.title CONTAINS $query OR d.content CONTAINS $query
            RETURN d as entity, 'TemplateDocument' as entity_type, score() as score, {} as path_info
            UNION
            MATCH (u:TemplateUser {organisation_id: $organisation_id})
            WHERE u.display_name CONTAINS $query OR u.username CONTAINS $query
            RETURN u as entity, 'TemplateUser' as entity_type, score() as score, {} as path_info
            UNION
            MATCH (p:TemplateProject {organisation_id: $organisation_id})
            WHERE p.name CONTAINS $query OR p.description CONTAINS $query
            RETURN p as entity, 'TemplateProject' as entity_type, score() as score, {} as path_info
        }
        RETURN entity, entity_type, score, path_info
        ORDER BY score DESC
        LIMIT $limit
        """

    # Placeholder methods for other search strategies - CUSTOMIZE as needed

    async def _entity_centric_search(self, query: str, organisation_id: str,
                                   search_type: TemplateSearchType, limit: int,
                                   filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Entity-centric search focusing on specific entity properties"""
        # CUSTOMIZE: Implement entity-centric search logic
        return await self._graph_traversal_search(query, organisation_id, search_type, limit, filters)

    async def _relationship_centric_search(self, query: str, organisation_id: str,
                                         search_type: TemplateSearchType, limit: int,
                                         filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Relationship-centric search focusing on connections between entities"""
        # CUSTOMIZE: Implement relationship-centric search logic
        return await self._graph_traversal_search(query, organisation_id, search_type, limit, filters)

    # Cache management methods

    def _generate_cache_key(self, query: str, organisation_id: str,
                           search_type: TemplateSearchType, strategy: SearchStrategy,
                           filters: Dict[str, Any] = None) -> str:
        """Generate cache key for search results"""
        key_data = {
            'query': query,
            'organisation_id': organisation_id,
            'search_type': search_type.value,
            'strategy': strategy.value,
            'filters': filters or {}
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return f"template_search:{hashlib.md5(key_string.encode()).hexdigest()}"

    def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached search result"""
        try:
            if self.redis_service:
                cached_data = self.redis_service.get(cache_key)
                if cached_data:
                    return json.loads(cached_data)
        except Exception as e:
            logger.warning(f"Failed to get cached result: {str(e)}")
        return None

    def _cache_result(self, cache_key: str, result: Dict[str, Any]):
        """Cache search result"""
        try:
            if self.redis_service:
                self.redis_service.setex(
                    cache_key,
                    self.cache_ttl,
                    json.dumps(result, default=str)
                )
        except Exception as e:
            logger.warning(f"Failed to cache result: {str(e)}")

    async def _fallback_search(self, query: str, organisation_id: str,
                              search_type: TemplateSearchType, limit: int,
                              filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Enhanced fallback search with specialized entity index optimization.

        This method is called when primary search strategies fail and provides
        a reliable backup using simple property matching and full-text indexes.

        CUSTOMIZE: Update the fallback queries for your specific entities.

        Args:
            query: Search query string
            organisation_id: Organisation ID
            search_type: Type of Template entity to search
            limit: Maximum results to return
            filters: Additional search filters

        Returns:
            Dict containing fallback search results
        """
        try:
            start_time = datetime.now()
            logger.info(f"Executing Template fallback search for query: '{query}'")

            # Try specialized entity search first
            if search_type != TemplateSearchType.ALL:
                specialized_results = await self._specialized_entity_search(
                    query, organisation_id, search_type, limit
                )
                if specialized_results.get('total_count', 0) > 0:
                    specialized_results['strategy_used'] = 'fallback_specialized'
                    return specialized_results

            # Fall back to simple property search
            simple_results = await self._simple_property_fallback(
                query, organisation_id, limit
            )

            search_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            simple_results['search_time_ms'] = search_time_ms
            simple_results['strategy_used'] = 'fallback_simple'

            return simple_results

        except Exception as e:
            logger.error(f"Template fallback search failed: {str(e)}")
            return {
                'results': [],
                'total_count': 0,
                'search_time_ms': 0,
                'strategy_used': 'fallback_failed',
                'error': str(e)
            }

    async def _specialized_entity_search(self, query: str, organisation_id: str,
                                       search_type: TemplateSearchType, limit: int) -> Dict[str, Any]:
        """
        Execute specialized search using entity-specific full-text indexes.

        CUSTOMIZE: Update the full-text search queries for your entities.

        Args:
            query: Search query string
            organisation_id: Organisation ID
            search_type: Type of Template entity to search
            limit: Maximum results to return

        Returns:
            Dict containing specialized search results
        """
        try:
            # Prepare query for full-text search
            prepared_query = self._prepare_fulltext_query(query)

            # Build entity-specific full-text query
            if search_type == TemplateSearchType.DOCUMENT:
                cypher_query = """
                CALL db.index.fulltext.queryNodes('template_document_fulltext', $query)
                YIELD node, score
                WHERE node.organisation_id = $organisation_id
                RETURN node as entity, 'TemplateDocument' as entity_type, score
                ORDER BY score DESC
                LIMIT $limit
                """
            elif search_type == TemplateSearchType.USER:
                cypher_query = """
                CALL db.index.fulltext.queryNodes('template_user_fulltext', $query)
                YIELD node, score
                WHERE node.organisation_id = $organisation_id
                RETURN node as entity, 'TemplateUser' as entity_type, score
                ORDER BY score DESC
                LIMIT $limit
                """
            elif search_type == TemplateSearchType.PROJECT:
                cypher_query = """
                CALL db.index.fulltext.queryNodes('template_project_fulltext', $query)
                YIELD node, score
                WHERE node.organisation_id = $organisation_id
                RETURN node as entity, 'TemplateProject' as entity_type, score
                ORDER BY score DESC
                LIMIT $limit
                """
            else:
                # Generic full-text search across all entities
                cypher_query = """
                CALL {
                    CALL db.index.fulltext.queryNodes('template_document_fulltext', $query)
                    YIELD node, score
                    WHERE node.organisation_id = $organisation_id
                    RETURN node as entity, 'TemplateDocument' as entity_type, score
                    UNION
                    CALL db.index.fulltext.queryNodes('template_user_fulltext', $query)
                    YIELD node, score
                    WHERE node.organisation_id = $organisation_id
                    RETURN node as entity, 'TemplateUser' as entity_type, score
                    UNION
                    CALL db.index.fulltext.queryNodes('template_project_fulltext', $query)
                    YIELD node, score
                    WHERE node.organisation_id = $organisation_id
                    RETURN node as entity, 'TemplateProject' as entity_type, score
                }
                RETURN entity, entity_type, score
                ORDER BY score DESC
                LIMIT $limit
                """

            # Execute query
            from app.services.neo4j_service import execute_read_query
            params = {
                'query': prepared_query,
                'organisation_id': organisation_id,
                'limit': limit
            }

            neo4j_results = execute_read_query(cypher_query, params)

            # Process results
            results = self._process_fulltext_results(neo4j_results, query)

            return {
                'results': results,
                'total_count': len(results),
                'search_time_ms': 0,  # Will be set by caller
                'strategy_used': 'specialized_fulltext'
            }

        except Exception as e:
            logger.error(f"Specialized entity search failed: {str(e)}")
            return {
                'results': [],
                'total_count': 0,
                'search_time_ms': 0,
                'strategy_used': 'specialized_failed',
                'error': str(e)
            }

    async def _simple_property_fallback(self, query: str, organisation_id: str, limit: int) -> Dict[str, Any]:
        """
        Ultimate fallback using simple property search when full-text indexes fail.

        CUSTOMIZE: Update the property search queries for your entities.

        Args:
            query: Search query string
            organisation_id: Organisation ID
            limit: Maximum results to return

        Returns:
            Dict containing simple property search results
        """
        try:
            logger.info(f"Executing simple property fallback search for query: '{query}'")

            # Simple property-based search across all entities
            cypher_query = """
            CALL {
                MATCH (d:TemplateDocument {organisation_id: $organisation_id})
                WHERE d.title CONTAINS $query OR d.content CONTAINS $query OR d.author CONTAINS $query
                RETURN d as entity, 'TemplateDocument' as entity_type, 1.0 as score
                UNION
                MATCH (u:TemplateUser {organisation_id: $organisation_id})
                WHERE u.display_name CONTAINS $query OR u.username CONTAINS $query OR u.email CONTAINS $query
                RETURN u as entity, 'TemplateUser' as entity_type, 1.0 as score
                UNION
                MATCH (p:TemplateProject {organisation_id: $organisation_id})
                WHERE p.name CONTAINS $query OR p.description CONTAINS $query
                RETURN p as entity, 'TemplateProject' as entity_type, 1.0 as score
            }
            RETURN entity, entity_type, score
            ORDER BY entity_type, score DESC
            LIMIT $limit
            """

            # Execute query
            from app.services.neo4j_service import execute_read_query
            params = {
                'query': query,
                'organisation_id': organisation_id,
                'limit': limit
            }

            neo4j_results = execute_read_query(cypher_query, params)

            # Process results
            results = []
            for record in neo4j_results:
                entity = record.get('entity')
                if entity:
                    results.append({
                        'id': entity.get('id'),
                        'title': self._get_entity_title(entity, record.get('entity_type', '')),
                        'content': self._get_entity_description(entity, record.get('entity_type', '')),
                        'entity_type': record.get('entity_type', ''),
                        'score': record.get('score', 1.0),
                        'metadata': dict(entity),
                        'search_method': 'simple_property'
                    })

            return {
                'results': results,
                'total_count': len(results),
                'search_time_ms': 0,  # Will be set by caller
                'strategy_used': 'simple_property'
            }

        except Exception as e:
            logger.error(f"Simple property fallback failed: {str(e)}")
            return {
                'results': [],
                'total_count': 0,
                'search_time_ms': 0,
                'strategy_used': 'simple_property_failed',
                'error': str(e)
            }

    def _prepare_fulltext_query(self, query: str) -> str:
        """
        Prepare query for Neo4j full-text search with proper escaping and operators.

        CUSTOMIZE: Update the query preparation logic for your search needs.

        Args:
            query: Raw search query

        Returns:
            str: Prepared query for full-text search
        """
        if not query or not query.strip():
            return "*"

        # Clean and prepare the query
        cleaned_query = query.strip()

        # Escape special characters that might break Neo4j full-text search
        special_chars = ['(', ')', '[', ']', '{', '}', '"', "'", '\\', '/', '?', '*', '+', '-', '!', '~', '^']
        for char in special_chars:
            cleaned_query = cleaned_query.replace(char, f'\\{char}')

        # Split into words and create OR query for better matching
        words = cleaned_query.split()
        if len(words) > 1:
            # Create fuzzy search with OR operator
            fuzzy_words = [f"{word}~" for word in words if len(word) > 2]
            if fuzzy_words:
                return " OR ".join(fuzzy_words)

        # Single word or fallback
        if len(cleaned_query) > 2:
            return f"{cleaned_query}~"  # Fuzzy search
        else:
            return cleaned_query

    def _process_fulltext_results(self, results: List[Dict], query: str) -> List[Dict]:
        """
        Process full-text search results with enhanced metadata.

        CUSTOMIZE: Update the result processing for your entity structures.

        Args:
            results: Raw Neo4j results
            query: Original search query

        Returns:
            List of processed search results
        """
        processed = []

        for record in results:
            entity = record.get('entity')
            if not entity:
                continue

            entity_type = record.get('entity_type', '')
            score = record.get('score', 0.0)

            # Calculate enhanced relevance score
            relevance_score = self._calculate_relevance_score(entity, query)
            final_score = max(float(score), relevance_score)

            processed_result = {
                'id': entity.get('id'),
                'title': self._get_entity_title(entity, entity_type),
                'content': self._get_entity_description(entity, entity_type),
                'entity_type': entity_type,
                'score': final_score,
                'metadata': dict(entity),
                'search_method': 'fulltext',
                'query_match_info': self._analyze_query_match(entity, query, entity_type)
            }

            processed.append(processed_result)

        # Sort by score descending
        processed.sort(key=lambda x: x.get('score', 0), reverse=True)

        return processed

    def _get_entity_title(self, entity: Dict, entity_type: str) -> str:
        """
        Get appropriate title based on entity type.

        CUSTOMIZE: Update the title extraction for your entity types.

        Args:
            entity: Entity data dictionary
            entity_type: Type of the entity

        Returns:
            str: Entity title
        """
        if entity_type == 'TemplateDocument':
            return entity.get('title', entity.get('id', ''))
        elif entity_type == 'TemplateUser':
            return entity.get('display_name', entity.get('username', entity.get('id', '')))
        elif entity_type == 'TemplateProject':
            return entity.get('name', entity.get('id', ''))
        else:
            # Generic fallback
            return entity.get('title', entity.get('name', entity.get('display_name', entity.get('id', ''))))

    def _get_entity_description(self, entity: Dict, entity_type: str) -> str:
        """
        Get appropriate description based on entity type.

        CUSTOMIZE: Update the description extraction for your entity types.

        Args:
            entity: Entity data dictionary
            entity_type: Type of the entity

        Returns:
            str: Entity description
        """
        if entity_type == 'TemplateDocument':
            content = entity.get('content', '')
            # Truncate long content
            if len(content) > 500:
                return content[:500] + "..."
            return content
        elif entity_type == 'TemplateUser':
            parts = []
            if entity.get('email'):
                parts.append(f"Email: {entity['email']}")
            if entity.get('role'):
                parts.append(f"Role: {entity['role']}")
            return " | ".join(parts) if parts else entity.get('username', '')
        elif entity_type == 'TemplateProject':
            return entity.get('description', '')
        else:
            # Generic fallback
            return entity.get('description', entity.get('content', ''))

    def _calculate_relevance_score(self, entity: Dict, query: str) -> float:
        """
        Calculate relevance score for search results.

        CUSTOMIZE: Update the scoring algorithm for your entities.

        Args:
            entity: Entity data dictionary
            query: Search query

        Returns:
            float: Relevance score (0.0 to 1.0)
        """
        score = 0.0
        query_lower = query.lower()

        # Check different fields with different weights
        searchable_fields = {
            'title': 1.0,
            'name': 1.0,
            'display_name': 0.9,
            'content': 0.7,
            'description': 0.7,
            'username': 0.6,
            'email': 0.5,
            'author': 0.4
        }

        for field, weight in searchable_fields.items():
            field_value = entity.get(field, '')
            if isinstance(field_value, str) and field_value:
                field_lower = field_value.lower()

                # Exact match gets highest score
                if query_lower == field_lower:
                    score += weight * 1.0
                # Starts with query gets high score
                elif field_lower.startswith(query_lower):
                    score += weight * 0.8
                # Contains query gets medium score
                elif query_lower in field_lower:
                    score += weight * 0.6
                # Word boundary match gets lower score
                elif any(word in field_lower for word in query_lower.split()):
                    score += weight * 0.4

        # Normalize score to 0-1 range
        return min(score, 1.0)

    def _analyze_query_match(self, entity: Dict, query: str, entity_type: str) -> Dict[str, Any]:
        """
        Analyze how the query matches the entity for debugging and ranking.

        CUSTOMIZE: Update the match analysis for your entity types.

        Args:
            entity: Entity data dictionary
            query: Search query
            entity_type: Type of the entity

        Returns:
            Dict containing match analysis
        """
        query_lower = query.lower()
        matches = {
            'exact_matches': [],
            'partial_matches': [],
            'word_matches': [],
            'field_scores': {}
        }

        # Analyze matches in different fields
        searchable_fields = ['title', 'name', 'display_name', 'content', 'description', 'username', 'email', 'author']

        for field in searchable_fields:
            field_value = entity.get(field, '')
            if isinstance(field_value, str) and field_value:
                field_lower = field_value.lower()

                if query_lower == field_lower:
                    matches['exact_matches'].append(field)
                    matches['field_scores'][field] = 1.0
                elif query_lower in field_lower:
                    matches['partial_matches'].append(field)
                    matches['field_scores'][field] = 0.7
                elif any(word in field_lower for word in query_lower.split()):
                    matches['word_matches'].append(field)
                    matches['field_scores'][field] = 0.4

        return matches

    def _process_graph_results(self, results: List[Dict], query: str) -> List[Dict]:
        """
        Process and format graph search results.

        CUSTOMIZE: Update the result processing for your graph structure.

        Args:
            results: Raw Neo4j results
            query: Original search query

        Returns:
            List of processed search results
        """
        processed = []

        for record in results:
            entity = record.get('entity')
            if not entity:
                continue

            entity_type = record.get('entity_type', '')

            processed_result = {
                'id': entity.get('id'),
                'title': self._get_entity_title(entity, entity_type),
                'content': self._get_entity_description(entity, entity_type),
                'entity_type': entity_type,
                'score': record.get('score', 1.0),
                'metadata': dict(entity),
                'search_method': 'graph_traversal',
                'path_info': record.get('path_info', {}),
                'relevance_score': self._calculate_relevance_score(entity, query)
            }

            processed.append(processed_result)

        # Sort by relevance score
        processed.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)

        return processed

    def _merge_search_results(self, results_list: List[List[Dict]], query: str) -> List[Dict]:
        """
        Merge results from multiple search strategies.

        CUSTOMIZE: Update the merging logic for your specific needs.

        Args:
            results_list: List of result lists from different strategies
            query: Original search query

        Returns:
            List of merged and deduplicated results
        """
        merged = []
        seen_ids = set()

        # Flatten and deduplicate results
        for results in results_list:
            for result in results:
                result_id = result.get('id')
                if result_id and result_id not in seen_ids:
                    merged.append(result)
                    seen_ids.add(result_id)
                elif result_id in seen_ids:
                    # Boost score for items found in multiple strategies
                    for existing in merged:
                        if existing.get('id') == result_id:
                            existing['score'] = max(existing.get('score', 0), result.get('score', 0)) * 1.1
                            existing['search_method'] = 'multi_strategy'
                            break

        # Sort by score
        merged.sort(key=lambda x: x.get('score', 0), reverse=True)

        return merged

    def _build_filter_conditions(self, filters: Dict[str, Any], search_type: TemplateSearchType) -> Dict[str, Any]:
        """
        Build WHERE conditions from filters.

        CUSTOMIZE: Update the filter conditions for your entity types.

        Args:
            filters: Filter dictionary
            search_type: Type of Template entity being searched

        Returns:
            Dict containing processed filter conditions
        """
        if not filters:
            return {}

        processed_filters = {}

        # Common filters for all entity types
        if 'created_after' in filters:
            processed_filters['created_after'] = filters['created_after']
        if 'created_before' in filters:
            processed_filters['created_before'] = filters['created_before']
        if 'updated_after' in filters:
            processed_filters['updated_after'] = filters['updated_after']
        if 'updated_before' in filters:
            processed_filters['updated_before'] = filters['updated_before']

        # Entity-specific filters
        if search_type == TemplateSearchType.DOCUMENT:
            if 'status' in filters:
                processed_filters['status'] = filters['status']
            if 'author' in filters:
                processed_filters['author'] = filters['author']
            if 'tags' in filters:
                processed_filters['tags'] = filters['tags']
        elif search_type == TemplateSearchType.USER:
            if 'role' in filters:
                processed_filters['role'] = filters['role']
            if 'is_active' in filters:
                processed_filters['is_active'] = filters['is_active']
        elif search_type == TemplateSearchType.PROJECT:
            if 'status' in filters:
                processed_filters['status'] = filters['status']
            if 'visibility' in filters:
                processed_filters['visibility'] = filters['visibility']

        return processed_filters

    def _extract_entities_from_query(self, query: str) -> List[str]:
        """
        Extract potential entity names from query.

        CUSTOMIZE: Update the entity extraction logic for your domain.

        Args:
            query: Search query string

        Returns:
            List of potential entity names
        """
        # Simple entity extraction - can be enhanced with NLP
        words = query.split()
        potential_entities = []

        # Look for capitalized words (potential proper nouns)
        for word in words:
            if word and word[0].isupper() and len(word) > 2:
                potential_entities.append(word.lower())

        # Look for quoted phrases
        import re
        quoted_phrases = re.findall(r'"([^"]*)"', query)
        potential_entities.extend([phrase.lower() for phrase in quoted_phrases])

        return potential_entities

    def _extract_relationship_patterns(self, query: str) -> List[str]:
        """
        Extract relationship patterns from query.

        CUSTOMIZE: Update the relationship pattern extraction for your domain.

        Args:
            query: Search query string

        Returns:
            List of potential relationship patterns
        """
        patterns = []
        query_lower = query.lower()

        # Common relationship indicators
        relationship_keywords = {
            'created by': 'creates',
            'authored by': 'creates',
            'belongs to': 'belongs_to',
            'member of': 'member_of',
            'part of': 'part_of',
            'references': 'references',
            'mentions': 'references',
            'similar to': 'similar_to',
            'related to': 'related_to'
        }

        for keyword, relationship in relationship_keywords.items():
            if keyword in query_lower:
                patterns.append(relationship)

        return patterns

    async def _determine_optimal_strategy(self, query: str, search_type: TemplateSearchType,
                                        filters: Dict[str, Any]) -> SearchStrategy:
        """
        Intelligently determine the best search strategy.

        CUSTOMIZE: Update the strategy selection logic for your use case.

        Args:
            query: Search query string
            search_type: Type of Template entity to search
            filters: Additional search filters

        Returns:
            SearchStrategy: Optimal strategy for the query
        """
        # Simple heuristics for strategy selection
        query_lower = query.lower()

        # If query contains relationship keywords, use relationship-centric search
        relationship_patterns = self._extract_relationship_patterns(query)
        if relationship_patterns:
            return SearchStrategy.RELATIONSHIP_CENTRIC

        # If query is very specific (quoted or has specific entity names), use graph traversal
        if '"' in query or any(word[0].isupper() for word in query.split() if word):
            return SearchStrategy.GRAPH_TRAVERSAL

        # If query is conceptual or semantic, use semantic search
        semantic_indicators = ['similar', 'like', 'about', 'regarding', 'concept', 'idea']
        if any(indicator in query_lower for indicator in semantic_indicators):
            return SearchStrategy.SEMANTIC_ONLY

        # If searching specific entity type, use entity-centric
        if search_type != TemplateSearchType.ALL:
            return SearchStrategy.ENTITY_CENTRIC

        # Default to hybrid for best coverage
        return SearchStrategy.HYBRID
