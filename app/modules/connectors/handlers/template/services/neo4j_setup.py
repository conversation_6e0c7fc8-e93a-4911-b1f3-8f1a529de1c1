"""
Template Connector Neo4j Setup

This module handles Neo4j database setup for the template connector,
including constraints, indexes, and initial data setup.

CUSTOMIZATION INSTRUCTIONS:
1. Replace "Template" with your connector name throughout this file
2. Update the constraint and index definitions for your entities
3. Add any connector-specific database setup logic
"""

import structlog
from app.services.neo4j_service import execute_write_query

logger = structlog.get_logger()


def create_template_constraints():
    """
    Create Neo4j constraints for Template entities.
    
    CUSTOMIZE: Update these constraints for your specific entities.
    Constraints ensure data integrity and improve query performance.
    """
    constraints = [
        # Document constraints - CUSTOMIZE for your primary entity
        """
        CREATE CONSTRAINT template_document_id IF NOT EXISTS
        FOR (d:TemplateDocument) REQUIRE (d.id, d.organisation_id) IS UNIQUE
        """,
        
        # User constraints - CUSTOMIZE for your user entity
        """
        CREATE CONSTRAINT template_user_id IF NOT EXISTS
        FOR (u:TemplateUser) REQUIRE (u.id, u.organisation_id) IS UNIQUE
        """,
        
        # Project constraints - CUSTOMIZE for your organizational entity
        """
        CREATE CONSTRAINT template_project_id IF NOT EXISTS
        FOR (p:TemplateProject) REQUIRE (p.id, p.organisation_id) IS UNIQUE
        """,
        
        # Username uniqueness within organisation
        """
        CREATE CONSTRAINT template_user_username IF NOT EXISTS
        FOR (u:TemplateUser) REQUIRE (u.username, u.organisation_id) IS UNIQUE
        """,
        
        # Add more constraints as needed for your entities
    ]
    
    for constraint in constraints:
        try:
            execute_write_query(constraint.strip(), {})
            logger.info(f"Created Template constraint: {constraint.strip().split()[2]}")
        except Exception as e:
            # Constraint might already exist
            logger.debug(f"Template constraint creation result: {str(e)}")


def create_template_indexes():
    """
    Create Neo4j indexes for Template entities.
    
    CUSTOMIZE: Update these indexes for your specific query patterns.
    Indexes improve query performance for frequently searched properties.
    """
    indexes = [
        # Document indexes - CUSTOMIZE for your searchable fields
        """
        CREATE INDEX template_document_title IF NOT EXISTS
        FOR (d:TemplateDocument) ON (d.title)
        """,
        
        """
        CREATE INDEX template_document_content IF NOT EXISTS
        FOR (d:TemplateDocument) ON (d.content)
        """,
        
        """
        CREATE INDEX template_document_author IF NOT EXISTS
        FOR (d:TemplateDocument) ON (d.author)
        """,
        
        """
        CREATE INDEX template_document_status IF NOT EXISTS
        FOR (d:TemplateDocument) ON (d.status)
        """,
        
        """
        CREATE INDEX template_document_updated_at IF NOT EXISTS
        FOR (d:TemplateDocument) ON (d.updated_at)
        """,
        
        # User indexes - CUSTOMIZE for your user fields
        """
        CREATE INDEX template_user_display_name IF NOT EXISTS
        FOR (u:TemplateUser) ON (u.display_name)
        """,
        
        """
        CREATE INDEX template_user_email IF NOT EXISTS
        FOR (u:TemplateUser) ON (u.email)
        """,
        
        """
        CREATE INDEX template_user_role IF NOT EXISTS
        FOR (u:TemplateUser) ON (u.role)
        """,
        
        # Project indexes - CUSTOMIZE for your project fields
        """
        CREATE INDEX template_project_name IF NOT EXISTS
        FOR (p:TemplateProject) ON (p.name)
        """,
        
        """
        CREATE INDEX template_project_status IF NOT EXISTS
        FOR (p:TemplateProject) ON (p.status)
        """,
        
        """
        CREATE INDEX template_project_visibility IF NOT EXISTS
        FOR (p:TemplateProject) ON (p.visibility)
        """,
        
        # Organisation indexes for all entities
        """
        CREATE INDEX template_document_organisation_id IF NOT EXISTS
        FOR (d:TemplateDocument) ON (d.organisation_id)
        """,
        
        """
        CREATE INDEX template_user_organisation_id IF NOT EXISTS
        FOR (u:TemplateUser) ON (u.organisation_id)
        """,
        
        """
        CREATE INDEX template_project_organisation_id IF NOT EXISTS
        FOR (p:TemplateProject) ON (p.organisation_id)
        """,
        
        # Sync-related indexes
        """
        CREATE INDEX template_document_last_synced_at IF NOT EXISTS
        FOR (d:TemplateDocument) ON (d.last_synced_at)
        """,
        
        """
        CREATE INDEX template_user_last_synced_at IF NOT EXISTS
        FOR (u:TemplateUser) ON (d.last_synced_at)
        """,
        
        """
        CREATE INDEX template_project_last_synced_at IF NOT EXISTS
        FOR (p:TemplateProject) ON (p.last_synced_at)
        """,
        
        # Add more indexes as needed for your query patterns
    ]
    
    for index in indexes:
        try:
            execute_write_query(index.strip(), {})
            logger.info(f"Created Template index: {index.strip().split()[2]}")
        except Exception as e:
            # Index might already exist
            logger.debug(f"Template index creation result: {str(e)}")


def create_template_fulltext_indexes():
    """
    Create full-text search indexes for Template entities.
    
    CUSTOMIZE: Update these for your searchable text fields.
    Full-text indexes enable efficient text search across large content.
    """
    fulltext_indexes = [
        # Document full-text search - CUSTOMIZE for your content fields
        """
        CREATE FULLTEXT INDEX template_document_fulltext IF NOT EXISTS
        FOR (d:TemplateDocument) ON EACH [d.title, d.content]
        """,
        
        # User full-text search - CUSTOMIZE for your user searchable fields
        """
        CREATE FULLTEXT INDEX template_user_fulltext IF NOT EXISTS
        FOR (u:TemplateUser) ON EACH [u.display_name, u.username, u.email]
        """,
        
        # Project full-text search - CUSTOMIZE for your project searchable fields
        """
        CREATE FULLTEXT INDEX template_project_fulltext IF NOT EXISTS
        FOR (p:TemplateProject) ON EACH [p.name, p.description]
        """,
        
        # Add more full-text indexes as needed
    ]
    
    for index in fulltext_indexes:
        try:
            execute_write_query(index.strip(), {})
            logger.info(f"Created Template full-text index: {index.strip().split()[3]}")
        except Exception as e:
            # Index might already exist
            logger.debug(f"Template full-text index creation result: {str(e)}")


def setup_template_database():
    """
    Complete database setup for Template connector.
    
    This function should be called during connector initialization
    to ensure all necessary database structures are in place.
    """
    try:
        logger.info("Setting up Template connector database structures")
        
        # Create constraints first (they're more important for data integrity)
        create_template_constraints()
        
        # Create regular indexes
        create_template_indexes()
        
        # Create full-text indexes
        create_template_fulltext_indexes()
        
        logger.info("Template connector database setup completed successfully")
        
    except Exception as e:
        logger.error(f"Template connector database setup failed: {str(e)}")
        raise


def cleanup_template_database(organisation_id: str):
    """
    Clean up Template connector data for a specific organisation.
    
    CUSTOMIZE: Update the cleanup logic for your entities.
    
    Args:
        organisation_id: Organisation ID to clean up
    """
    try:
        logger.info(f"Cleaning up Template connector data for organisation: {organisation_id}")
        
        cleanup_queries = [
            # Delete all Template entities for the organisation
            """
            MATCH (d:TemplateDocument {organisation_id: $organisation_id})
            DETACH DELETE d
            """,
            
            """
            MATCH (u:TemplateUser {organisation_id: $organisation_id})
            DETACH DELETE u
            """,
            
            """
            MATCH (p:TemplateProject {organisation_id: $organisation_id})
            DETACH DELETE p
            """,
            
            # Add cleanup for any other entities
        ]
        
        total_deleted = 0
        for query in cleanup_queries:
            result = execute_write_query(query.strip(), {'organisation_id': organisation_id})
            # Note: The exact way to get deletion count depends on your Neo4j driver
            # This is a placeholder - adjust based on your implementation
            deleted_count = result.summary().counters.nodes_deleted if hasattr(result, 'summary') else 0
            total_deleted += deleted_count
        
        logger.info(f"Template connector cleanup completed. Deleted {total_deleted} entities for organisation {organisation_id}")
        
    except Exception as e:
        logger.error(f"Template connector cleanup failed: {str(e)}")
        raise


def validate_template_database_setup() -> bool:
    """
    Validate that Template connector database setup is correct.
    
    Returns:
        bool: True if setup is valid, False otherwise
    """
    try:
        # Check if constraints exist
        constraint_check = """
        SHOW CONSTRAINTS
        YIELD name, labelsOrTypes, properties
        WHERE any(label IN labelsOrTypes WHERE label STARTS WITH 'Template')
        RETURN count(*) as constraint_count
        """
        
        # Check if indexes exist
        index_check = """
        SHOW INDEXES
        YIELD name, labelsOrTypes, properties
        WHERE any(label IN labelsOrTypes WHERE label STARTS WITH 'Template')
        RETURN count(*) as index_count
        """
        
        constraint_result = execute_write_query(constraint_check, {})
        index_result = execute_write_query(index_check, {})
        
        # CUSTOMIZE: Adjust these counts based on your actual constraints and indexes
        expected_constraints = 4  # Update based on your constraint count
        expected_indexes = 15     # Update based on your index count
        
        constraint_count = constraint_result[0]['constraint_count'] if constraint_result else 0
        index_count = index_result[0]['index_count'] if index_result else 0
        
        if constraint_count >= expected_constraints and index_count >= expected_indexes:
            logger.info(f"Template database validation passed: {constraint_count} constraints, {index_count} indexes")
            return True
        else:
            logger.warning(f"Template database validation failed: {constraint_count}/{expected_constraints} constraints, {index_count}/{expected_indexes} indexes")
            return False
            
    except Exception as e:
        logger.error(f"Template database validation failed: {str(e)}")
        return False
