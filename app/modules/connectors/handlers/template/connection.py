"""
Template Connector Connection Management

This module handles connection management for the template connector.
Replace "Template" with your connector name and implement your specific connection logic.

CUSTOMIZATION INSTRUCTIONS:
1. Replace "Template" with your connector name throughout this file
2. Implement your specific connection logic (API clients, database connections, etc.)
3. Update the authentication and credential handling
4. Add any connector-specific connection validation
5. Update error handling for your specific connection types
"""

import logging
from typing import Dict, Any, Optional
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from app.utils.source_credentials import get_source_credentials

logger = logging.getLogger(__name__)


class TemplateConnection:
    """
    Template connector connection management.
    
    CUSTOMIZE: Replace with your specific connection implementation.
    This could be:
    - REST API client
    - Database connection
    - File system access
    - Message queue connection
    - etc.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Template connection.
        
        Args:
            config: Connection configuration dictionary
        """
        self.config = config
        self.organisation_id = config.get('organisation_id')
        self.timeout = config.get('timeout_seconds', 30)
        self.max_retries = config.get('max_retries', 3)
        
        # Connection state
        self._client = None
        self._connected = False
        self._credentials = None
        
        # CUSTOMIZE: Add your connector-specific initialization
        self.base_url = config.get('base_url', 'https://api.template.com')  # Replace with your API
        self.api_version = config.get('api_version', 'v1')
        
    def connect(self) -> Any:
        """
        Establish connection to the Template data source.
        
        CUSTOMIZE: Implement your specific connection logic here.
        
        Returns:
            Connection client or session object
            
        Raises:
            ConnectionError: If connection fails
        """
        try:
            logger.info(f"Connecting to Template data source for organisation: {self.organisation_id}")
            
            # Get credentials
            self._credentials = self._get_credentials()
            
            # CUSTOMIZE: Replace with your connection logic
            # Examples:
            # - Create API client with authentication
            # - Establish database connection
            # - Set up file system access
            # - Initialize message queue connection
            
            # Example: REST API client setup
            self._client = self._create_api_client()
            
            # Test the connection
            if not self._test_connection():
                raise ConnectionError("Connection test failed")
            
            self._connected = True
            logger.info("Successfully connected to Template data source")
            
            return self._client
            
        except Exception as e:
            logger.error(f"Failed to connect to Template data source: {str(e)}")
            raise ConnectionError(f"Template connection failed: {str(e)}")
    
    def disconnect(self):
        """
        Disconnect from the Template data source.
        
        CUSTOMIZE: Implement your specific disconnection logic.
        """
        try:
            if self._client:
                # CUSTOMIZE: Add your disconnection logic
                # Examples:
                # - Close API sessions
                # - Close database connections
                # - Clean up resources
                
                # Example: Close requests session
                if hasattr(self._client, 'close'):
                    self._client.close()
                
                self._client = None
                self._connected = False
                logger.info("Disconnected from Template data source")
                
        except Exception as e:
            logger.error(f"Error disconnecting from Template data source: {str(e)}")
    
    def is_connected(self) -> bool:
        """
        Check if connection is active.
        
        Returns:
            bool: True if connected, False otherwise
        """
        return self._connected and self._client is not None
    
    def get_client(self) -> Any:
        """
        Get the connection client.
        
        Returns:
            Connection client object
            
        Raises:
            ConnectionError: If not connected
        """
        if not self.is_connected():
            raise ConnectionError("Not connected to Template data source")
        
        return self._client
    
    def _get_credentials(self) -> Dict[str, Any]:
        """
        Get credentials for the Template data source.
        
        CUSTOMIZE: Update for your credential requirements.
        
        Returns:
            Dict containing credentials
            
        Raises:
            ConnectionError: If credentials cannot be retrieved
        """
        try:
            # CUSTOMIZE: Update credential type and structure
            # Common patterns:
            # - API tokens/keys
            # - Username/password
            # - OAuth tokens
            # - Service account credentials
            # - Connection strings
            
            credentials = get_source_credentials(
                self.organisation_id,
                "template",  # CUSTOMIZE: your source type
                credential_type="api_key"  # CUSTOMIZE: your credential type
            )
            
            if not credentials:
                raise ConnectionError("No credentials found for Template data source")
            
            # CUSTOMIZE: Validate required credential fields
            required_fields = ['api_key']  # Update based on your requirements
            for field in required_fields:
                if field not in credentials:
                    raise ConnectionError(f"Missing required credential field: {field}")
            
            return credentials
            
        except Exception as e:
            logger.error(f"Failed to get Template credentials: {str(e)}")
            raise ConnectionError(f"Credential retrieval failed: {str(e)}")
    
    def _create_api_client(self) -> requests.Session:
        """
        Create API client for Template data source.
        
        CUSTOMIZE: Replace with your specific client creation logic.
        This is just an example using requests.Session for REST APIs.
        
        Returns:
            Configured API client
        """
        session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=self.max_retries,
            status_forcelist=[429, 500, 502, 503, 504],
            backoff_factor=1,
            allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Set headers
        session.headers.update({
            'Authorization': f"Bearer {self._credentials['api_key']}",  # CUSTOMIZE
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'RuhOrg-Template-Connector/1.0'  # CUSTOMIZE
        })
        
        # Set timeout
        session.timeout = self.timeout
        
        return session
    
    def _test_connection(self) -> bool:
        """
        Test the connection to Template data source.
        
        CUSTOMIZE: Implement your specific connection test.
        
        Returns:
            bool: True if connection test passes, False otherwise
        """
        try:
            # CUSTOMIZE: Replace with your connection test logic
            # Examples:
            # - Make a simple API call (e.g., GET /health, GET /user/me)
            # - Query a test table/collection
            # - Check authentication status
            # - Validate permissions
            
            # Example: Test API endpoint
            response = self._client.get(f"{self.base_url}/{self.api_version}/health")
            
            if response.status_code == 200:
                logger.debug("Template connection test successful")
                return True
            else:
                logger.warning(f"Template connection test failed with status: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Template connection test failed: {str(e)}")
            return False
    
    def refresh_credentials(self):
        """
        Refresh credentials if needed.
        
        CUSTOMIZE: Implement credential refresh logic if your system supports it.
        """
        try:
            logger.info("Refreshing Template credentials")
            
            # Get new credentials
            new_credentials = self._get_credentials()
            
            # Update client with new credentials
            if self._client and new_credentials:
                # CUSTOMIZE: Update client authentication
                self._client.headers.update({
                    'Authorization': f"Bearer {new_credentials['api_key']}"
                })
                
                self._credentials = new_credentials
                logger.info("Template credentials refreshed successfully")
                
        except Exception as e:
            logger.error(f"Failed to refresh Template credentials: {str(e)}")
            raise ConnectionError(f"Credential refresh failed: {str(e)}")
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()
