import os
import structlog
from app.modules.connectors.utilities.schema_loader import ConnectorSchema, ConnectorSchemaError

logger = structlog.get_logger()

# Pre-initialized schemas for Template connector
try:
    schema_path = os.path.join(os.path.dirname(__file__), "template_schema.yml")
    template_schema = ConnectorSchema("template", schema_path)
except ConnectorSchemaError as e:
    logger.warning(f"Could not load Template schema: {e}")
    template_schema = None
