# Template Connector Schema
version: 1.0
description: "Template schema for creating new connectors following the established pattern."

# INSTRUCTIONS FOR CUSTOMIZATION:
# 1. Replace "Template" with your connector name (e.g., "<PERSON>lack", "<PERSON><PERSON>", "Confluence")
# 2. Define your specific node types with their properties
# 3. Define your specific relationship types
# 4. Update embedding_metadata to specify what data goes to Pinecone
# 5. Ensure all entities have organisation_id for multi-tenancy

nodes:
  # Example: Primary entity (replace with your main entity type)
  TemplateDocument:
    description: "Represents a document in the template system"
    properties:
      id:
        type: string
        required: true
        description: "Unique document ID from the source system"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      title:
        type: string
        description: "Document title"
      content:
        type: string
        description: "Document content"
      author:
        type: string
        description: "Document author"
      status:
        type: string
        description: "Document status (draft, published, archived)"
      tags:
        type: array
        description: "List of tags associated with the document"
      created_at:
        type: timestamp
        description: "Document creation time"
      updated_at:
        type: timestamp
        description: "Document last update time"

  # Example: User entity (most connectors have users)
  TemplateUser:
    description: "Represents a user in the template system"
    properties:
      id:
        type: string
        required: true
        description: "Unique user ID from the source system"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      username:
        type: string
        description: "User's username"
      display_name:
        type: string
        description: "User's display name"
      email:
        type: string
        description: "User's email address"
      role:
        type: string
        description: "User's role in the system"
      is_active:
        type: boolean
        description: "Whether the user is active"
      created_at:
        type: timestamp
        description: "User creation time"
      updated_at:
        type: timestamp
        description: "User last update time"

  # Example: Project/Container entity (many systems have organizational containers)
  TemplateProject:
    description: "Represents a project or workspace in the template system"
    properties:
      id:
        type: string
        required: true
        description: "Unique project ID from the source system"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this entity"
      name:
        type: string
        description: "Project name"
      description:
        type: string
        description: "Project description"
      status:
        type: string
        description: "Project status (active, archived, etc.)"
      visibility:
        type: string
        description: "Project visibility (public, private, etc.)"
      created_at:
        type: timestamp
        description: "Project creation time"
      updated_at:
        type: timestamp
        description: "Project last update time"

relationships:
  # Example: User creates document
  CREATES:
    from: TemplateUser
    to: TemplateDocument
    description: "User creates a document"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When the document was created"

  # Example: User belongs to project
  MEMBER_OF:
    from: TemplateUser
    to: TemplateProject
    description: "User is a member of a project"
    direction: "->"
    properties:
      role:
        type: string
        description: "User's role in the project"
      joined_at:
        type: timestamp
        description: "When the user joined the project"

  # Example: Document belongs to project
  BELONGS_TO:
    from: TemplateDocument
    to: TemplateProject
    description: "Document belongs to a project"
    direction: "->"
    properties:
      added_at:
        type: timestamp
        description: "When the document was added to the project"

  # Example: Documents can reference each other
  REFERENCES:
    from: TemplateDocument
    to: TemplateDocument
    description: "Document references another document"
    direction: "->"
    properties:
      reference_type:
        type: string
        description: "Type of reference (link, mention, etc.)"
      created_at:
        type: timestamp
        description: "When the reference was created"

# Embedding metadata configuration
# This section defines what data should be stored in Pinecone for each entity type
embedding_metadata:
  TemplateDocument:
    # Text fields to embed (will be concatenated for embedding generation)
    embed_fields:
      - title
      - content
    # Metadata to store with the embedding in Pinecone
    metadata_fields:
      - id
      - title
      - author
      - status
      - tags
      - created_at
      - updated_at
      - organisation_id
    # Chunking strategy for large content
    chunking:
      enabled: true
      chunk_size: 1000
      overlap: 200

  TemplateUser:
    embed_fields:
      - display_name
      - email
    metadata_fields:
      - id
      - username
      - display_name
      - email
      - role
      - organisation_id
    chunking:
      enabled: false

  TemplateProject:
    embed_fields:
      - name
      - description
    metadata_fields:
      - id
      - name
      - description
      - status
      - visibility
      - organisation_id
    chunking:
      enabled: false
