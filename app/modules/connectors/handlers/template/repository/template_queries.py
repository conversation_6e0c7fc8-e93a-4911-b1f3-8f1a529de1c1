"""
Template Connector Neo4j Queries

This module contains all Neo4j queries for the template connector,
organized by entity type and functionality following the established patterns.

CUSTOMIZATION INSTRUCTIONS:
1. Replace "Template" with your connector name throughout this file
2. Update the entity labels to match your schema (e.g., TemplateDocument -> SlackMessage)
3. Update the property names to match your schema definition
4. Add queries for your specific entity types and relationships
5. Update the constraint and index creation queries
"""

from app.modules.connectors.handlers.template.models.schema_loader import template_schema
from app.modules.organisation.models.schema_loader import schema as organisation_schema
from app.modules.agents.models.schema_loader import agent_schema
import structlog

logger = structlog.get_logger()


class TemplateEntityQueries:
    """
    Neo4j queries for Template entity operations.
    
    CUSTOMIZE: Update these queries for your specific entity types.
    """
    
    # Document queries - CUSTOMIZE for your primary content entity
    CREATE_OR_UPDATE_DOCUMENT = """
    MERGE (d:TemplateDocument {id: $id, organisation_id: $organisation_id})
    SET d.title = $title,
        d.content = $content,
        d.author = $author,
        d.status = $status,
        d.tags = $tags,
        d.created_at = $created_at,
        d.updated_at = $updated_at,
        d.last_synced_at = datetime()
    RETURN d
    """
    
    GET_DOCUMENT_BY_ID = """
    MATCH (d:TemplateDocument {id: $id, organisation_id: $organisation_id})
    RETURN d
    """
    
    GET_DOCUMENTS_BY_PROJECT = """
    MATCH (d:TemplateDocument)-[:BELONGS_TO]->(p:TemplateProject {id: $project_id})
    WHERE d.organisation_id = $organisation_id
    RETURN d
    ORDER BY d.updated_at DESC
    LIMIT $limit
    """
    
    DELETE_DOCUMENT = """
    MATCH (d:TemplateDocument {id: $id, organisation_id: $organisation_id})
    DETACH DELETE d
    """
    
    # User queries - CUSTOMIZE for your user entity
    CREATE_OR_UPDATE_USER = """
    MERGE (u:TemplateUser {id: $id, organisation_id: $organisation_id})
    SET u.username = $username,
        u.display_name = $display_name,
        u.email = $email,
        u.role = $role,
        u.is_active = $is_active,
        u.created_at = $created_at,
        u.updated_at = $updated_at,
        u.last_synced_at = datetime()
    RETURN u
    """
    
    GET_USER_BY_ID = """
    MATCH (u:TemplateUser {id: $id, organisation_id: $organisation_id})
    RETURN u
    """
    
    GET_USER_BY_USERNAME = """
    MATCH (u:TemplateUser {username: $username, organisation_id: $organisation_id})
    RETURN u
    """
    
    GET_ACTIVE_USERS = """
    MATCH (u:TemplateUser {organisation_id: $organisation_id, is_active: true})
    RETURN u
    ORDER BY u.display_name
    LIMIT $limit
    """
    
    DELETE_USER = """
    MATCH (u:TemplateUser {id: $id, organisation_id: $organisation_id})
    DETACH DELETE u
    """
    
    # Project queries - CUSTOMIZE for your organizational entity
    CREATE_OR_UPDATE_PROJECT = """
    MERGE (p:TemplateProject {id: $id, organisation_id: $organisation_id})
    SET p.name = $name,
        p.description = $description,
        p.status = $status,
        p.visibility = $visibility,
        p.created_at = $created_at,
        p.updated_at = $updated_at,
        p.last_synced_at = datetime()
    RETURN p
    """
    
    GET_PROJECT_BY_ID = """
    MATCH (p:TemplateProject {id: $id, organisation_id: $organisation_id})
    RETURN p
    """
    
    GET_PROJECTS_BY_STATUS = """
    MATCH (p:TemplateProject {organisation_id: $organisation_id, status: $status})
    RETURN p
    ORDER BY p.name
    LIMIT $limit
    """
    
    DELETE_PROJECT = """
    MATCH (p:TemplateProject {id: $id, organisation_id: $organisation_id})
    DETACH DELETE p
    """
    
    # Search queries - CUSTOMIZE for your search needs
    SEARCH_DOCUMENTS = """
    MATCH (d:TemplateDocument {organisation_id: $organisation_id})
    WHERE d.title CONTAINS $query OR d.content CONTAINS $query
    RETURN d, score() as relevance_score
    ORDER BY relevance_score DESC
    LIMIT $limit
    """
    
    SEARCH_ALL_ENTITIES = """
    CALL {
        MATCH (d:TemplateDocument {organisation_id: $organisation_id})
        WHERE d.title CONTAINS $query OR d.content CONTAINS $query
        RETURN d as entity, 'TemplateDocument' as entity_type, score() as relevance_score
        UNION
        MATCH (u:TemplateUser {organisation_id: $organisation_id})
        WHERE u.display_name CONTAINS $query OR u.username CONTAINS $query
        RETURN u as entity, 'TemplateUser' as entity_type, score() as relevance_score
        UNION
        MATCH (p:TemplateProject {organisation_id: $organisation_id})
        WHERE p.name CONTAINS $query OR p.description CONTAINS $query
        RETURN p as entity, 'TemplateProject' as entity_type, score() as relevance_score
    }
    RETURN entity, entity_type, relevance_score
    ORDER BY relevance_score DESC
    LIMIT $limit
    """


class TemplateRelationshipQueries:
    """
    Neo4j queries for Template relationship operations.
    
    CUSTOMIZE: Update these queries for your specific relationship types.
    """
    
    # User creates document
    CREATE_USER_CREATES_DOCUMENT = """
    MATCH (u:TemplateUser {id: $user_id, organisation_id: $organisation_id})
    MATCH (d:TemplateDocument {id: $document_id, organisation_id: $organisation_id})
    MERGE (u)-[r:CREATES]->(d)
    SET r.created_at = $created_at
    RETURN r
    """
    
    # User is member of project
    CREATE_USER_MEMBER_OF_PROJECT = """
    MATCH (u:TemplateUser {id: $user_id, organisation_id: $organisation_id})
    MATCH (p:TemplateProject {id: $project_id, organisation_id: $organisation_id})
    MERGE (u)-[r:MEMBER_OF]->(p)
    SET r.role = $role,
        r.joined_at = $joined_at
    RETURN r
    """
    
    # Document belongs to project
    CREATE_DOCUMENT_BELONGS_TO_PROJECT = """
    MATCH (d:TemplateDocument {id: $document_id, organisation_id: $organisation_id})
    MATCH (p:TemplateProject {id: $project_id, organisation_id: $organisation_id})
    MERGE (d)-[r:BELONGS_TO]->(p)
    SET r.added_at = $added_at
    RETURN r
    """
    
    # Document references another document
    CREATE_DOCUMENT_REFERENCES_DOCUMENT = """
    MATCH (d1:TemplateDocument {id: $source_document_id, organisation_id: $organisation_id})
    MATCH (d2:TemplateDocument {id: $target_document_id, organisation_id: $organisation_id})
    MERGE (d1)-[r:REFERENCES]->(d2)
    SET r.reference_type = $reference_type,
        r.created_at = $created_at
    RETURN r
    """
    
    # Get user's documents
    GET_USER_DOCUMENTS = """
    MATCH (u:TemplateUser {id: $user_id, organisation_id: $organisation_id})-[:CREATES]->(d:TemplateDocument)
    RETURN d
    ORDER BY d.updated_at DESC
    LIMIT $limit
    """
    
    # Get project members
    GET_PROJECT_MEMBERS = """
    MATCH (u:TemplateUser)-[r:MEMBER_OF]->(p:TemplateProject {id: $project_id, organisation_id: $organisation_id})
    RETURN u, r.role as role, r.joined_at as joined_at
    ORDER BY u.display_name
    """
    
    # Get project documents
    GET_PROJECT_DOCUMENTS = """
    MATCH (d:TemplateDocument)-[:BELONGS_TO]->(p:TemplateProject {id: $project_id, organisation_id: $organisation_id})
    RETURN d
    ORDER BY d.updated_at DESC
    LIMIT $limit
    """
    
    # Get document references
    GET_DOCUMENT_REFERENCES = """
    MATCH (d:TemplateDocument {id: $document_id, organisation_id: $organisation_id})-[r:REFERENCES]->(ref:TemplateDocument)
    RETURN ref, r.reference_type as reference_type
    """
    
    # Delete relationship
    DELETE_RELATIONSHIP = """
    MATCH (a)-[r:$relationship_type]->(b)
    WHERE a.organisation_id = $organisation_id 
    AND b.organisation_id = $organisation_id
    AND id(r) = $relationship_id
    DELETE r
    """


class TemplateSyncQueries:
    """
    Neo4j queries for Template sync operations.
    
    CUSTOMIZE: Update these queries for your sync needs.
    """
    
    # Get entities that need syncing (modified since last sync)
    GET_ENTITIES_TO_SYNC = """
    MATCH (n:TemplateDocument {organisation_id: $organisation_id})
    WHERE n.updated_at > $last_sync_time
    OR n.last_synced_at IS NULL
    RETURN n, labels(n) as entity_type
    ORDER BY n.updated_at
    LIMIT $batch_size
    """
    
    # Mark entities as synced
    MARK_ENTITIES_SYNCED = """
    MATCH (n {organisation_id: $organisation_id})
    WHERE n.id IN $entity_ids
    SET n.last_synced_at = datetime()
    RETURN count(n) as updated_count
    """
    
    # Get sync statistics
    GET_SYNC_STATS = """
    MATCH (n {organisation_id: $organisation_id})
    WHERE n:TemplateDocument OR n:TemplateUser OR n:TemplateProject
    RETURN 
        labels(n)[0] as entity_type,
        count(n) as total_count,
        count(CASE WHEN n.last_synced_at IS NOT NULL THEN 1 END) as synced_count,
        max(n.last_synced_at) as last_sync_time
    """
    
    # Clean up orphaned entities (entities that no longer exist in source)
    CLEANUP_ORPHANED_ENTITIES = """
    MATCH (n {organisation_id: $organisation_id})
    WHERE n:TemplateDocument OR n:TemplateUser OR n:TemplateProject
    AND n.last_synced_at < $cleanup_threshold
    DETACH DELETE n
    RETURN count(n) as deleted_count
    """
    
    # Create constraints and indexes
    CREATE_CONSTRAINTS = """
    CREATE CONSTRAINT template_document_id IF NOT EXISTS
    FOR (d:TemplateDocument) REQUIRE (d.id, d.organisation_id) IS UNIQUE;
    
    CREATE CONSTRAINT template_user_id IF NOT EXISTS
    FOR (u:TemplateUser) REQUIRE (u.id, u.organisation_id) IS UNIQUE;
    
    CREATE CONSTRAINT template_project_id IF NOT EXISTS
    FOR (p:TemplateProject) REQUIRE (p.id, p.organisation_id) IS UNIQUE;
    """
    
    CREATE_INDEXES = """
    CREATE INDEX template_document_title IF NOT EXISTS
    FOR (d:TemplateDocument) ON (d.title);
    
    CREATE INDEX template_document_updated_at IF NOT EXISTS
    FOR (d:TemplateDocument) ON (d.updated_at);
    
    CREATE INDEX template_user_username IF NOT EXISTS
    FOR (u:TemplateUser) ON (u.username);
    
    CREATE INDEX template_project_name IF NOT EXISTS
    FOR (p:TemplateProject) ON (p.name);
    """
