"""
Template Connector Schema Definitions

This module contains Pydantic schema definitions for the template connector,
including configuration schemas and data models.

CUSTOMIZATION INSTRUCTIONS:
1. Replace "Template" with your connector name throughout this file
2. Update the configuration fields to match your connector's needs
3. Add any connector-specific validation logic
4. Update the data models to match your API responses
5. Add any additional schema classes as needed
"""

from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime
from enum import Enum


class TemplateConnectorConfig(BaseModel):
    """
    Configuration schema for Template connector.
    
    CUSTOMIZE: Update these fields to match your connector's configuration needs.
    """
    
    # Required fields
    organisation_id: str = Field(..., description="Organisation ID for credential lookup")
    
    # Connection settings - CUSTOMIZE for your data source
    base_url: str = Field(
        default="https://api.template.com",  # CUSTOMIZE: your API base URL
        description="Template API base URL"
    )
    api_version: str = Field(
        default="v1",
        description="API version to use"
    )
    timeout_seconds: int = Field(
        default=30,
        description="Request timeout in seconds",
        ge=1,
        le=300
    )
    max_retries: int = Field(
        default=3,
        description="Maximum number of retry attempts",
        ge=0,
        le=10
    )
    
    # Rate limiting
    rate_limit_per_hour: int = Field(
        default=1000,  # CUSTOMIZE: based on your API limits
        description="Rate limit per hour",
        ge=1
    )
    
    # Sync settings
    batch_size: int = Field(
        default=100,
        description="Batch size for processing",
        ge=1,
        le=1000
    )
    full_sync: bool = Field(
        default=False,
        description="Whether to perform full sync"
    )
    
    # Entity sync toggles - CUSTOMIZE for your entities
    sync_documents: bool = Field(
        default=True,
        description="Whether to sync documents"
    )
    sync_users: bool = Field(
        default=True,
        description="Whether to sync users"
    )
    sync_projects: bool = Field(
        default=True,
        description="Whether to sync projects"
    )
    
    # Content processing
    extract_text: bool = Field(
        default=True,
        description="Whether to extract text content"
    )
    generate_embeddings: bool = Field(
        default=True,
        description="Whether to generate embeddings"
    )
    chunk_size: int = Field(
        default=1000,
        description="Text chunk size for embeddings",
        ge=100,
        le=4000
    )
    chunk_overlap: int = Field(
        default=200,
        description="Overlap between text chunks",
        ge=0,
        le=1000
    )
    
    # Search settings
    search_limit: int = Field(
        default=20,
        description="Default search result limit",
        ge=1,
        le=100
    )
    
    # Advanced settings - CUSTOMIZE as needed
    enable_real_time_updates: bool = Field(
        default=False,
        description="Whether to enable real-time updates"
    )
    webhook_url: Optional[str] = Field(
        default=None,
        description="Webhook URL for real-time updates"
    )
    custom_fields: Dict[str, Any] = Field(
        default_factory=dict,
        description="Custom configuration fields"
    )
    
    @validator('chunk_overlap')
    def validate_chunk_overlap(cls, v, values):
        """Ensure chunk overlap is less than chunk size"""
        chunk_size = values.get('chunk_size', 1000)
        if v >= chunk_size:
            raise ValueError('chunk_overlap must be less than chunk_size')
        return v
    
    @validator('base_url')
    def validate_base_url(cls, v):
        """Ensure base URL is properly formatted"""
        if not v.startswith(('http://', 'https://')):
            raise ValueError('base_url must start with http:// or https://')
        return v.rstrip('/')
    
    class Config:
        """Pydantic configuration"""
        extra = "allow"  # Allow additional fields
        validate_assignment = True


# Data Models - CUSTOMIZE these to match your API responses

class TemplateDocument(BaseModel):
    """Template document data model - CUSTOMIZE for your document structure"""
    
    id: str = Field(..., description="Unique document ID")
    title: str = Field(default="", description="Document title")
    content: str = Field(default="", description="Document content")
    author: str = Field(default="", description="Document author")
    status: str = Field(default="draft", description="Document status")
    tags: List[str] = Field(default_factory=list, description="Document tags")
    created_at: Optional[datetime] = Field(default=None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(default=None, description="Last update timestamp")
    
    class Config:
        extra = "allow"


class TemplateUser(BaseModel):
    """Template user data model - CUSTOMIZE for your user structure"""
    
    id: str = Field(..., description="Unique user ID")
    username: str = Field(default="", description="Username")
    display_name: str = Field(default="", description="Display name")
    email: str = Field(default="", description="Email address")
    role: str = Field(default="user", description="User role")
    is_active: bool = Field(default=True, description="Whether user is active")
    created_at: Optional[datetime] = Field(default=None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(default=None, description="Last update timestamp")
    
    class Config:
        extra = "allow"


class TemplateProject(BaseModel):
    """Template project data model - CUSTOMIZE for your project structure"""
    
    id: str = Field(..., description="Unique project ID")
    name: str = Field(default="", description="Project name")
    description: str = Field(default="", description="Project description")
    status: str = Field(default="active", description="Project status")
    visibility: str = Field(default="private", description="Project visibility")
    created_at: Optional[datetime] = Field(default=None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(default=None, description="Last update timestamp")
    
    class Config:
        extra = "allow"


# Enum definitions - CUSTOMIZE for your specific values

class TemplateDocumentStatus(str, Enum):
    """Template document status values - CUSTOMIZE for your statuses"""
    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"
    DELETED = "deleted"


class TemplateUserRole(str, Enum):
    """Template user role values - CUSTOMIZE for your roles"""
    ADMIN = "admin"
    EDITOR = "editor"
    VIEWER = "viewer"
    USER = "user"


class TemplateProjectStatus(str, Enum):
    """Template project status values - CUSTOMIZE for your statuses"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ARCHIVED = "archived"
    DELETED = "deleted"


class TemplateProjectVisibility(str, Enum):
    """Template project visibility values - CUSTOMIZE for your visibility options"""
    PUBLIC = "public"
    PRIVATE = "private"
    INTERNAL = "internal"


# Response models for API endpoints

class TemplateSearchResponse(BaseModel):
    """Template search response model"""
    
    query: str = Field(..., description="Original search query")
    results: List[Dict[str, Any]] = Field(default_factory=list, description="Search results")
    total_count: int = Field(default=0, description="Total number of results")
    search_time_ms: float = Field(default=0.0, description="Search time in milliseconds")
    strategy_used: str = Field(default="", description="Search strategy used")
    filters_applied: Dict[str, Any] = Field(default_factory=dict, description="Filters applied")
    
    class Config:
        extra = "allow"


class TemplateSyncResponse(BaseModel):
    """Template sync response model"""
    
    success: bool = Field(..., description="Whether sync was successful")
    message: str = Field(default="", description="Sync status message")
    entities_synced: int = Field(default=0, description="Number of entities synced")
    sync_type: str = Field(default="incremental", description="Type of sync performed")
    start_time: Optional[datetime] = Field(default=None, description="Sync start time")
    end_time: Optional[datetime] = Field(default=None, description="Sync end time")
    errors: List[str] = Field(default_factory=list, description="Any errors encountered")
    
    class Config:
        extra = "allow"


class TemplateConnectionStatus(BaseModel):
    """Template connection status model"""
    
    connected: bool = Field(..., description="Whether connection is active")
    last_test_time: Optional[datetime] = Field(default=None, description="Last connection test time")
    connection_details: Dict[str, Any] = Field(default_factory=dict, description="Connection details")
    error_message: Optional[str] = Field(default=None, description="Error message if connection failed")
    
    class Config:
        extra = "allow"


# Validation helpers - CUSTOMIZE as needed

def validate_template_config(config: Dict[str, Any]) -> TemplateConnectorConfig:
    """
    Validate and parse Template connector configuration.
    
    Args:
        config: Raw configuration dictionary
        
    Returns:
        Validated TemplateConnectorConfig instance
        
    Raises:
        ValidationError: If configuration is invalid
    """
    return TemplateConnectorConfig(**config)


def validate_template_document(data: Dict[str, Any]) -> TemplateDocument:
    """
    Validate and parse Template document data.
    
    Args:
        data: Raw document data dictionary
        
    Returns:
        Validated TemplateDocument instance
        
    Raises:
        ValidationError: If data is invalid
    """
    return TemplateDocument(**data)
