"""
Template Connector Entity Types

This module defines all entity types for the template connector following the GitHub pattern.
Replace "Template" with your connector name and update the entity types to match your schema.

CUSTOMIZATION INSTRUCTIONS:
1. Replace "Template" with your connector name (e.g., "<PERSON>lack", "<PERSON><PERSON>", "Confluence")
2. Update the entity types to match your schema definition
3. Update the categorization functions to group your entities logically
4. Update the mapping functions for your specific entity types
"""

from enum import Enum
from typing import Set


class EntityType(Enum):
    """
    Template entity types for knowledge graph nodes.
    
    Define all possible node types that can be created in the knowledge graph
    for Template data, organized by relevance groups.
    
    CUSTOMIZE: Replace these with your actual entity types
    """
    
    # Core entities (Tier 1) - Most important for basic functionality
    TEMPLATE_DOCUMENT = "TemplateDocument"
    TEMPLATE_USER = "TemplateUser"
    TEMPLATE_PROJECT = "TemplateProject"
    
    # Extended entities (Tier 2) - Additional functionality
    # Add more entity types as needed for your connector
    # TEMPLATE_COMMENT = "TemplateComment"
    # TEMPLATE_ATTACHMENT = "TemplateAttachment"
    # TEMPLATE_WORKFLOW = "TemplateWorkflow"


def get_all_entity_types() -> Set[str]:
    """
    Returns all Template entity types for connector registration.
    
    Returns:
        set: Set of all entity type string values
    """
    return {e.value for e in EntityType}


def get_core_entities() -> Set[str]:
    """
    Returns core entity types that are essential for basic functionality.
    
    Returns:
        set: Set of core entity type string values
    """
    return {
        EntityType.TEMPLATE_DOCUMENT.value,
        EntityType.TEMPLATE_USER.value,
        EntityType.TEMPLATE_PROJECT.value,
    }


def get_user_related_entities() -> Set[str]:
    """
    Returns user-related entity types.
    
    Returns:
        set: Set of user-related entity type string values
    """
    return {
        EntityType.TEMPLATE_USER.value,
    }


def get_content_entities() -> Set[str]:
    """
    Returns content-related entity types.
    
    Returns:
        set: Set of content entity type string values
    """
    return {
        EntityType.TEMPLATE_DOCUMENT.value,
    }


def get_organizational_entities() -> Set[str]:
    """
    Returns organizational structure entity types.
    
    Returns:
        set: Set of organizational entity type string values
    """
    return {
        EntityType.TEMPLATE_PROJECT.value,
    }


def is_valid_entity_type(entity_type: str) -> bool:
    """
    Check if an entity type is valid for this connector.
    
    Args:
        entity_type: The entity type to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    return entity_type in get_all_entity_types()


def get_entity_tier(entity_type: str) -> int:
    """
    Get the tier/priority level of an entity type.
    
    Args:
        entity_type: The entity type to check
        
    Returns:
        int: Tier level (1 = highest priority, higher numbers = lower priority)
    """
    if entity_type in get_core_entities():
        return 1
    else:
        return 2


def map_source_type_to_entity_type(source_type: str) -> str:
    """
    Map a source system type to our entity type.
    
    CUSTOMIZE: Update this mapping for your specific source system
    
    Args:
        source_type: The type from the source system
        
    Returns:
        str: The corresponding entity type
    """
    # Example mapping - customize for your source system
    template_type_mapping = {
        "document": EntityType.TEMPLATE_DOCUMENT.value,
        "doc": EntityType.TEMPLATE_DOCUMENT.value,
        "file": EntityType.TEMPLATE_DOCUMENT.value,
        "user": EntityType.TEMPLATE_USER.value,
        "person": EntityType.TEMPLATE_USER.value,
        "project": EntityType.TEMPLATE_PROJECT.value,
        "workspace": EntityType.TEMPLATE_PROJECT.value,
        "space": EntityType.TEMPLATE_PROJECT.value,
    }
    
    return template_type_mapping.get(
        source_type.lower(), EntityType.TEMPLATE_DOCUMENT.value
    )


def get_searchable_entity_types() -> Set[str]:
    """
    Returns entity types that should be included in search operations.
    
    Returns:
        set: Set of searchable entity type string values
    """
    # Most entities should be searchable, but you can exclude some if needed
    return get_all_entity_types()


def get_embeddable_entity_types() -> Set[str]:
    """
    Returns entity types that should have embeddings generated.
    
    Returns:
        set: Set of embeddable entity type string values
    """
    # Typically content entities should have embeddings
    return {
        EntityType.TEMPLATE_DOCUMENT.value,
        EntityType.TEMPLATE_PROJECT.value,
    }
