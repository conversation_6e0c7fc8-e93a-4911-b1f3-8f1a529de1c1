"""
Template Connector Constants

This module contains all constant definitions for the template connector,
including entity types and relationship types.
"""

from .entities import EntityType, get_all_entity_types
from .relationships import TemplateRelationshipType, get_all_relationship_types

__all__ = [
    'EntityType',
    'get_all_entity_types',
    'TemplateRelationshipType', 
    'get_all_relationship_types'
]
