"""
Template Connector Relationship Types

This module defines all relationship types for the template connector following the GitHub pattern.
Replace "Template" with your connector name and update the relationship types to match your schema.

CUSTOMIZATION INSTRUCTIONS:
1. Replace "Template" with your connector name (e.g., "<PERSON>lack", "<PERSON><PERSON>", "Confluence")
2. Update the relationship types to match your schema definition
3. Update the categorization functions to group your relationships logically
4. Add any connector-specific relationship validation logic
"""

from enum import Enum
from typing import Set


class TemplateRelationshipType(Enum):
    """Template-specific relationship types"""
    
    # User relationships
    CREATES = "creates"
    MEMBER_OF = "member_of"
    OWNS = "owns"
    ASSIGNED_TO = "assigned_to"
    
    # Content relationships
    BELONGS_TO = "belongs_to"
    REFERENCES = "references"
    CONTAINS = "contains"
    DEPENDS_ON = "depends_on"
    
    # Collaboration relationships
    COLLABORATES_ON = "collaborates_on"
    REVIEWS = "reviews"
    APPROVES = "approves"
    COMMENTS_ON = "comments_on"
    
    # Organizational relationships
    PART_OF = "part_of"
    MANAGES = "manages"
    REPORTS_TO = "reports_to"
    
    # Temporal relationships
    FOLLOWS = "follows"
    PRECEDES = "precedes"
    DERIVED_FROM = "derived_from"
    
    # Similarity relationships (for AI/ML features)
    SIMILAR_TO = "similar_to"
    RELATED_TO = "related_to"


def get_all_relationship_types() -> Set[str]:
    """Returns all Template relationship types for connector registration."""
    return {r.value for r in TemplateRelationshipType}


def get_user_relationships() -> Set[str]:
    """
    Returns relationship types involving users.
    
    Returns:
        set: Set of user-related relationship type string values
    """
    return {
        TemplateRelationshipType.CREATES.value,
        TemplateRelationshipType.MEMBER_OF.value,
        TemplateRelationshipType.OWNS.value,
        TemplateRelationshipType.ASSIGNED_TO.value,
        TemplateRelationshipType.COLLABORATES_ON.value,
        TemplateRelationshipType.REVIEWS.value,
        TemplateRelationshipType.APPROVES.value,
        TemplateRelationshipType.COMMENTS_ON.value,
        TemplateRelationshipType.MANAGES.value,
        TemplateRelationshipType.REPORTS_TO.value,
    }


def get_content_relationships() -> Set[str]:
    """
    Returns relationship types involving content entities.
    
    Returns:
        set: Set of content-related relationship type string values
    """
    return {
        TemplateRelationshipType.BELONGS_TO.value,
        TemplateRelationshipType.REFERENCES.value,
        TemplateRelationshipType.CONTAINS.value,
        TemplateRelationshipType.DEPENDS_ON.value,
        TemplateRelationshipType.DERIVED_FROM.value,
        TemplateRelationshipType.SIMILAR_TO.value,
        TemplateRelationshipType.RELATED_TO.value,
    }


def get_organizational_relationships() -> Set[str]:
    """
    Returns relationship types for organizational structure.
    
    Returns:
        set: Set of organizational relationship type string values
    """
    return {
        TemplateRelationshipType.PART_OF.value,
        TemplateRelationshipType.MANAGES.value,
        TemplateRelationshipType.REPORTS_TO.value,
        TemplateRelationshipType.MEMBER_OF.value,
    }


def get_temporal_relationships() -> Set[str]:
    """
    Returns relationship types that represent temporal connections.
    
    Returns:
        set: Set of temporal relationship type string values
    """
    return {
        TemplateRelationshipType.FOLLOWS.value,
        TemplateRelationshipType.PRECEDES.value,
        TemplateRelationshipType.DERIVED_FROM.value,
    }


def get_bidirectional_relationships() -> Set[str]:
    """
    Returns relationship types that are bidirectional.
    
    Returns:
        set: Set of bidirectional relationship type string values
    """
    return {
        TemplateRelationshipType.SIMILAR_TO.value,
        TemplateRelationshipType.RELATED_TO.value,
        TemplateRelationshipType.COLLABORATES_ON.value,
    }


def is_valid_relationship_type(relationship_type: str) -> bool:
    """
    Check if a relationship type is valid for this connector.
    
    Args:
        relationship_type: The relationship type to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    return relationship_type in get_all_relationship_types()


def get_relationship_direction(relationship_type: str) -> str:
    """
    Get the direction of a relationship type.
    
    Args:
        relationship_type: The relationship type to check
        
    Returns:
        str: Direction ('->' for directed, '<->' for bidirectional)
    """
    if relationship_type in get_bidirectional_relationships():
        return "<->"
    else:
        return "->"


def get_inverse_relationship(relationship_type: str) -> str:
    """
    Get the inverse of a relationship type if it exists.
    
    Args:
        relationship_type: The relationship type
        
    Returns:
        str: The inverse relationship type, or the same type if bidirectional/no inverse
    """
    # Define inverse relationships
    inverse_mapping = {
        TemplateRelationshipType.CREATES.value: "created_by",
        TemplateRelationshipType.OWNS.value: "owned_by",
        TemplateRelationshipType.MANAGES.value: "managed_by",
        TemplateRelationshipType.CONTAINS.value: TemplateRelationshipType.BELONGS_TO.value,
        TemplateRelationshipType.BELONGS_TO.value: TemplateRelationshipType.CONTAINS.value,
        TemplateRelationshipType.FOLLOWS.value: TemplateRelationshipType.PRECEDES.value,
        TemplateRelationshipType.PRECEDES.value: TemplateRelationshipType.FOLLOWS.value,
    }
    
    return inverse_mapping.get(relationship_type, relationship_type)


def get_relationship_weight(relationship_type: str) -> float:
    """
    Get the default weight/importance of a relationship type for graph algorithms.
    
    Args:
        relationship_type: The relationship type
        
    Returns:
        float: Weight value (higher = more important)
    """
    # Define relationship weights based on importance
    weight_mapping = {
        TemplateRelationshipType.CREATES.value: 1.0,
        TemplateRelationshipType.OWNS.value: 1.0,
        TemplateRelationshipType.BELONGS_TO.value: 0.9,
        TemplateRelationshipType.MEMBER_OF.value: 0.8,
        TemplateRelationshipType.REFERENCES.value: 0.7,
        TemplateRelationshipType.COLLABORATES_ON.value: 0.7,
        TemplateRelationshipType.SIMILAR_TO.value: 0.6,
        TemplateRelationshipType.RELATED_TO.value: 0.5,
        TemplateRelationshipType.COMMENTS_ON.value: 0.4,
    }
    
    return weight_mapping.get(relationship_type, 0.5)
